import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/wating_orders_dialog/waiting_orders_dialog.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/model/affiliate_program_response/affiliate_program_detail.dart';
import 'package:swadesic/model/affiliate_program_response/affiliate_program_response.dart';
import 'package:swadesic/model/app_startup_message/app_startup_message.dart';
import 'package:swadesic/model/base_url_response/base_url_response.dart';
import 'package:swadesic/services/app_remote_config/app_remote_config_set_default_data.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';

class AppRemoteConfigService{


  //region Common variables
  late FirebaseRemoteConfig firebaseRemoteConfig;
  late BaseUrlResponse baseUrlResponse;

  //endregion



  //region Constructor
  AppRemoteConfigService();
  //endregion


  //region Init
  Future<void> init() async {
    try {
      //Initialize firebase remote config
      firebaseRemoteConfig = FirebaseRemoteConfig.instance;

      //Set configuration for both web and mobile
      firebaseRemoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(seconds: 1),
      ));

      //Set default values based on platform
      if (!kIsWeb) {
        //Set default value for mobile
        AppRemoteConfigSetDefaultData().setDefaultValueForAppString(firebaseRemoteConfig: firebaseRemoteConfig);
        //Set default Affiliate program
        AppRemoteConfigSetDefaultData().setDefaultAffiliateProgram();
      }

      //Fetch and activate for both platforms
      await firebaseRemoteConfig.fetchAndActivate();
      
      //Get app string and affiliate program details for both platforms
      getAppString();
      getAffiliateProgramDetail();
      getAppStartupMessage();
      getAppMetaData();

      //Listen for updates on both platforms
      firebaseRemoteConfig.onConfigUpdated.listen((RemoteConfigUpdate update) async {
        await firebaseRemoteConfig.activate();
        getAppString();
        getAffiliateProgramDetail();
        getAppStartupMessage();
        getAppMetaData();
      });
      
    } catch (e) {
      debugPrint('Remote Config Error: $e');
    }
  }
//endregion


//region Get app string
  void getAppString() {
    var jsonDataAsString = firebaseRemoteConfig.getString('app_strings');
    //Convert string to json
    Map<String,dynamic> data = json.decode(jsonDataAsString);
    //Parse above string in to below model class
    AppStrings.fromJson(data);
    //print(jsonDataAsString);
  }
//endregion

//region Get app startup message
  void getAppStartupMessage() {
    try {
      var jsonDataAsString = firebaseRemoteConfig.getString('app_startup_message');
      if (jsonDataAsString.isNotEmpty) {
        //Convert string to json
        Map<String,dynamic> data = json.decode(jsonDataAsString);
        //Parse into AppStartupMessage model
        AppStartupMessage.fromJson(data);
        if (kDebugMode) {
          print('AppStartupMessage loaded: ${AppStartupMessage().toString()}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing app_startup_message: $e');
      }
    }
  }
//endregion


//region Get app startup message
  void getAppMetaData() {
    try {
      var jsonDataAsString = firebaseRemoteConfig.getString('app_meta_data');
      if (jsonDataAsString.isNotEmpty) {
        //Convert string to json
        Map<String,dynamic> data = json.decode(jsonDataAsString);
        BaseUrlResponse.fromJson(data);
        //Parse into AppStartupMessage model
        if (kDebugMode) {
          print('AppMetaData loaded successfully');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing app_startup_message: $e');
      }
    }
  }
//endregion




//region Get affiliate program detail
  void getAffiliateProgramDetail() {
    var jsonDataAsString = firebaseRemoteConfig.getString('affiliated_program_detail');
    //Convert string to json
    Map<String,dynamic> data = json.decode(jsonDataAsString);
    //Parse above string in to below model class
    AffiliateProgramDetail.fromJson(data);
    //print(jsonDataAsString);
  }
//endregion
}