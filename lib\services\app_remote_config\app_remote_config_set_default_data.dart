import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:swadesic/model/affiliate_program_response/affiliate_program_detail.dart';

class AppRemoteConfigSetDefaultData {




  //region Set default value fro appString
  void setDefaultValueForAppString({required FirebaseRemoteConfig firebaseRemoteConfig}){
    firebaseRemoteConfig.setDefaults(
        {
          "user_invitee_message": "Hey!\nI’m using Swadesic, a platform supporting Swadeshi products and small businesses. It’s a community where you can find great alternatives and help build a self-reliant Bharat—truly a strong boost for the vocal for local movement!\n\nYou can instantly start supporting small businesses across India and shop safely while platform manages payments, backed by a refund guarantee. Sellers can set up stores for free, start receiving orders, and build a loyal customer community",
          "store_invitee_message": "Hey!\nAs someone who runs a store on Swadesic, I’ve found a platform that genuinely supports Swadeshi products and small businesses. It’s a community where you can find great alternatives and help build a self-reliant Bharat—truly a strong boost for the vocal for local movement!\n\nYou can instantly start supporting small businesses across India and shop safely while platform manages payments, backed by a refund guarantee. Sellers can set up stores for free, start receiving orders, and build a loyal customer community",
            "other_sharing_a_product_message": "Check out this product!\nI found this on Swadesic, a Swadeshi Marketplace Community. Know more here:\n",
            "owner_sharing_a_product_message": "Check out my product!\nI’ve got this on my store at Swadesic, a community supporting local brands. Check it out here:\n",
            "other_sharing_a_store_message": "Check out this amazing store!\nI found this store on Swadesic, supporting Swadeshi products and small businesses. Explore their store & products here:\n",
            "owner_sharing_a_store_message": "Check out my store!\nI’m excited to share my store on Swadesic with you, where you can find great Swadeshi products. Check it out:\n",
            "app_startup_message": """
            {
              "show_message": false,
              "display_text": "Welcome to Swadesic! We're excited to have you here.",
              "link": "https://swadesic.com/welcome"
            }
          """,
        }

    );

  }
//endregion



//region Set default affiliate Program
void setDefaultAffiliateProgram(){
  AffiliateProgramDetail.fromJson({
    'title': 'Earn 20% Monthly Recurring Revenue',
    'description': 'Refer people to Swadesic and earn 20% of order revenue we earn from them for life. If anyone from your referrals opens a store, it’s automatically linked to you.',
    'steps': [
      {'text': 'Share your invite code or link with your friends, followers, or customers.'},
      {'text': 'When one of your referral creates a store, it is linked to you.'},
      {'text': "You'll earn 20% of order revenue Swadesic makes from that Store for life**"}

    ],
  });



}
//endregion



}