///Todo uncomment this for web app
import 'dart:html';

import 'package:flutter/foundation.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';



class WebAppCaptureUrl{


  ///Todo uncomment this for web app
  //region Constructor
  WebAppCaptureUrl(){
    //print("Previously saved Web url is : ${AppConstants.webChangedUrl}");

    if(AppConstants.webChangedUrl.isEmpty){
      var url = window.location.href;
      //If url contains ic then save in cache
      if(Uri.parse(url).queryParameters['ic'] != null ){
        AppDataService().saveReferralCode(referralCode: Uri.parse(url).queryParameters['ic']!);
      }
      AppConstants.webChangedUrl = url ;
      //print("Current incoming Web url is : $url");
    }
  }
  //endregion



  //region Has only referral
  bool hasOnlyReferral()  {
    Uri url = Uri.parse(AppConstants.webChangedUrl);

    if(url.queryParameters['ic'] != null && url.toString().split('/')[3].startsWith('d?ic=')){
      return true;
    }
    else{
      return false;
    }
  }
//endregion

//region Has super link
bool hasSuperLink()  {
    Uri url = Uri.parse(AppConstants.webChangedUrl);
  return (url.path.contains('super_link') || url.path.contains('super_link/'));
}
//endregion

}